resource "aws_iam_policy" "DevAccessPolicy" {
  name        = "DevAccessPolicy"
  description = "Provides developers access to production environments"
  policy = jsonencode(
    {
      Version = "2012-10-17"
      Statement = [
        {
          Effect   = "Allow"
          Resource = "*"
          Action = [
            // API Gateway
            "apigateway:GET",
            "apigateway:POST",
            "apigateway:PATCH",

            // Amplify
            "amplify:GetJob",
            "amplify:GetArtifactUrl",
            "amplify:ListJobs",
            "amplify:ListDomainAssociations",
            "amplify:ListBackendEnvironments",
            "amplify:GetBackendEnvironment",
            "amplify:ListWebHooks",
            "amplify:ListBranches",
            "amplify:ListApps",
            "amplify:GetBranch",
            "amplify:GetWebHook",
            "amplify:GetDomainAssociation",
            "amplify:ListArtifacts",
            "amplify:ListTagsForResource",
            "amplify:GetApp",

            // CloudFormation
            "cloudformation:Describe*",
            "cloudformation:List*",
            "cloudformation:Get*",

            // CloudWatch and Logs
            "cloudwatch:Describe*",
            "cloudwatch:Get*",
            "cloudwatch:List*",
            "logs:Get*",
            "logs:List*",
            "logs:StartQuery",
            "logs:StopQuery",
            "logs:Describe*",
            "logs:TestMetricFilter",
            "logs:FilterLogEvents",

            // Cognito
            "cognito-identity:Describe*",
            "cognito-identity:Get*",
            "cognito-identity:List*",
            "cognito-idp:Describe*",
            "cognito-idp:AdminGet*",
            "cognito-idp:AdminList*",
            "cognito-idp:List*",
            "cognito-idp:Get*",
            "cognito-sync:Describe*",
            "cognito-sync:Get*",
            "cognito-sync:List*",

            // DynamoDB
            "dynamodb:CreateTable",
            "dynamodb:DeleteTable",
            "dynamodb:DescribeTable",
            "dynamodb:DescribeKinesisStreamingDestination",
            "dynamodb:DescribeStream",
            "dynamodb:DescribeContinuousBackups",
            "dynamodb:ListBackups",
            "dynamodb:ListTables",
            "dynamodb:GetItem",
            "dynamodb:PutItem",
            "dynamodb:UpdateItem",
            "dynamodb:DeleteItem",
            "dynamodb:Query",
            "dynamodb:Scan",

            // EC2 and AutoScaling
            "ec2:DescribeInstances",
            "ec2:StartInstances",
            "ec2:StopInstances",
            "ec2:RebootInstances",
            "ec2:TerminateInstances",
            "ec2:GetInstanceMetadata",
            "ec2:DescribeSecurityGroups",
            "ec2:DescribeSubnets",
            "ec2:DescribeVpcs",
            "autoscaling:*",

            // CloudWatch + Event Bridge Events
            "events:Describe*",
            "events:List*",
            "events:TestEventPattern",
            "events:PutRule",
            "events:PutTargets",

            // IAM
            "iam:PutRolePolicy",
            "iam:GetPolicy",
            "iam:GetPolicyVersion",
            "iam:GetRole",
            "iam:GetRolePolicy",
            "iam:ListAttachedRolePolicies",
            "iam:ListRolePolicies",
            "iam:ListPolicies",
            "iam:ListRoles",
            "iam:PassRole",
            "iam:ListServerCertificates",
            "iam:CreateServiceLinkedRole",
            "iam:AttachRolePolicy",
            "iam:CreateRole",
            "iam:CreateInstanceProfile",
            "iam:AddRoleToInstanceProfile",

            // IoT
            "iot:AttachPrincipalPolicy",
            "iot:AttachThingPrincipal",
            "iot:GetThingShadow",
            "iot:ListNamedShadowsForThing",
            "iot:CreateKeysAndCertificate",
            "iot:CreatePolicy",
            "iot:CreateThing",
            "iot:CreateTopicRule",
            "iot:DescribeEndpoint",
            "iot:DescribeThing",
            "iot:GetTopicRule",
            "iot:ListPolicies",
            "iot:ListThings",
            "iot:ListTopicRules",
            "iot:ListFleetMetrics",
            "iot:ReplaceTopicRule",
            "iot:Connect",
            "iot:Receive",
            "iot:Subscribe",
            "iot:ListAuthorizers",
            "iot:DescribeAuthorizer",

            // Kinesis
            "kinesis:DescribeStream",
            "kinesis:PutRecord",
            "kinesis:PutRecords",
            "kinesis:GetShardIterator",
            "kinesis:GetRecords",
            "kinesis:ListShards",
            "kinesis:ListStreams",
            "kinesis:DescribeStreamSummary",
            "kinesis:RegisterStreamConsumer",

            // KMS
            "kms:ListAliases",

            // SSM
            "ssm:DescribeParameters",
            "ssm:GetParameter",
            "ssm:AddTagsToResource",
            "ssm:PutParameter",

            // Lambda
            "lambda:CreateFunction",
            "lambda:UpdateFunctionCode",
            "lambda:UpdateFunctionConfiguration",
            "lambda:InvokeFunction",
            "lambda:PublishVersion",
            "lambda:GetFunctionConfiguration",
            "lambda:Get*",
            "lambda:List*",
            "lambda:DeleteFunction",

            // S3
            "s3:List*",
            "s3:Get*",
            "s3:PutObject",
            "s3:DeleteObject",

            // SNS
            "sns:ListSubscriptions",
            "sns:ListSubscriptionsByTopic",
            "sns:ListTopics",
            "sns:Publish",
            "sns:Subscribe",
            "sns:Unsubscribe",
            "sns:GetSMSSandboxAccountStatus",

            // SQS
            "sqs:ListQueues",
            "sqs:GetQueueAttributes",
            "sqs:GetQueueUrl",
            "sqs:ListDeadLetterSourceQueues",
            "sqs:ListMessageMoveTasks",
            "sqs:ListQueueTags",
            "sqs:SendMessage",

            // Resource Tagging
            "tag:GetResources",

            // X-Ray
            "xray:PutTelemetryRecords",
            "xray:PutTraceSegments",

            // ECR
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:GetRepositoryPolicy",
            "ecr:DescribeRepositories",
            "ecr:ListImages",
            "ecr:DescribeImages",
            "ecr:BatchGetImage",
            "ecr:GetLifecyclePolicy",
            "ecr:GetLifecyclePolicyPreview",
            "ecr:ListTagsForResource",
            "ecr:DescribeImageScanFindings",

            // ACM
            "acm:ListCertificates",
            "acm:DescribeCertificate",

            // CloudFront
            "cloudfront:*",

            // WAF
            "waf:ListWebACLs",
            "waf:GetWebACL",
            "wafv2:ListWebACLs",
            "wafv2:GetWebACL",
            "wafv2:GetWebACLForResource",

            // Elastic Beanstalk
            "elasticbeanstalk:*",

            // ECS
            "ecs:CreateCluster",
            "ecs:DescribeClusters",
            "ecs:CreateTaskDefinition",
            "ecs:RegisterTaskDefinition",
            "ecs:DeregisterTaskDefinition",
            "ecs:DescribeTaskDefinition",
            "ecs:ListTaskDefinitions",
            "ecs:CreateService",
            "ecs:DeleteService",
            "ecs:DescribeServices",
            "ecs:UpdateService",
            "ecs:RegisterContainerInstance",
            "ecs:DeregisterContainerInstance",
            "ecs:DescribeContainerInstances",
            "ecs:ListContainerInstances",
            "ecs:RunTask",

            // Elastic Load Balancing
            "elasticloadbalancing:CreateListener",
            "elasticloadbalancing:CreateLoadBalancer",
            "elasticloadbalancing:CreateRule",
            "elasticloadbalancing:CreateTargetGroup",
            "elasticloadbalancing:DeleteListener",
            "elasticloadbalancing:DeleteLoadBalancer",
            "elasticloadbalancing:DeleteRule",
            "elasticloadbalancing:DeleteTargetGroup",
            "elasticloadbalancing:DescribeListeners",
            "elasticloadbalancing:DescribeLoadBalancers",
            "elasticloadbalancing:DescribeRules",
            "elasticloadbalancing:DescribeTargetGroups",
            "elasticloadbalancing:AddTags",

            // CodeBuild
            "codebuild:CreateProject",
            "codebuild:DeleteProject",
            "codebuild:BatchGetBuilds",
            "codebuild:StartBuild",

            // AWS Support
            "support:AddAttachmentsToSet",
            "support:AddCommunicationToCase",
            "support:CreateCase",
            "support:DescribeCases",
            "support:DescribeCommunications",
            "support:DescribeServices",
            "support:DescribeSeverityLevels",
            "support:DescribeTrustedAdvisorCheckRefreshStatuses",
            "support:DescribeTrustedAdvisorCheckResult",
            "support:DescribeTrustedAdvisorChecks",
            "support:DescribeTrustedAdvisorCheckSummaries",
            "support:DescribeCreateCaseOptions",
            "support:DescribeAttachment",
            "support:DescribeSupportLevel",
            "support:GetSupportLevel",
            "support:InitiateChatForCase",
            "support:ResolveCase",
            "support:SearchForCases",
            "trustedadvisor:Describe*",
            "trustedadvisor:Get*",
            "trustedadvisor:RefreshCheck",

            // Secrets Manager
            "secretsmanager:DescribeSecret",
            "secretsmanager:GetSecretValue",
            "secretsmanager:ListSecrets",
            "secretsmanager:PutSecretValue",
            "secretsmanager:UpdateSecret",

            // Event Bridge
            "schemas:Describe*",
            "schemas:ExportSchema",
            "schemas:Get*",
            "schemas:List*",
            "schemas:SearchSchemas",

            "scheduler:Get*",
            "scheduler:List*",

            "pipes:Describe*",
            "pipes:List*",

            // Backup
            "backup:Get*",
            "backup:List*",
            "backup:Describe*",
          ]
        }
      ]
    }
  )
}
