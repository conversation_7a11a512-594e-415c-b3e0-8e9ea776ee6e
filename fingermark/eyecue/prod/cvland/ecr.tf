
module "aggregateddata_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "aggregated-data"
  tags     = var.default_tags
}

module "alertmanager_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "alertmanager"
  tags     = var.default_tags
}

module "backgroundimagecollection_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "background-image-collection"
  tags     = var.default_tags
}

module "bestshotcapturer_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "best-shot-capturer"
  tags     = var.default_tags
}

module "blackbox_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "blackbox"
  tags     = var.default_tags
}

module "cameraconfigchanger_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-config-changer"
  tags     = var.default_tags
}

module "deepstream_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "deep-stream"
  tags     = var.default_tags
}

module "deepstreammsgdispatcher_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "deepstream-msg-dispatcher"
  tags     = var.default_tags
}

module "eyecueargus_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-argus"
  tags     = var.default_tags
}
module "eyecuegrafana_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-grafana"
  tags     = var.default_tags
}

module "eyeqcredentials_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-credentials"
  tags     = var.default_tags
}

module "eyeqdetector_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-detector"
  tags     = var.default_tags
}

module "eyecue_mosaic_assembler_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-mosaic-assembler"
  tags     = var.default_tags
}

module "eyecue_mosaic_recorder_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-mosaic-recorder"
  tags     = var.default_tags
}

# TODO: Remove this repo when it has been replaced by the new eyecue-mosaic-recorder repo
module "eyeqmosaic_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-mosaic"
  tags     = var.default_tags
}

# TODO: Remove this repo when it has been replaced by the new eyecue-mosaic-assembler repo
module "eyecueassembler_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-assembler"
  tags     = var.default_tags
}

module "eyeqserver_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-server"
  tags     = var.default_tags
}

module "eyeqtracker_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyeq-tracker"
  tags     = var.default_tags
}

module "nginx_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "nginx"
  tags     = var.default_tags
}

module "roisuggestorjobs_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "roi-suggestor-jobs"
  tags     = var.default_tags
}

module "trainingdatacapture_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "training-data-capture"
  tags     = var.default_tags
}

module "victoriametrics_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "victoria-metrics"
  tags     = var.default_tags
}

module "vmagent_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "vmagent"
  tags     = var.default_tags
}

module "vmalert_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "vmalert"
  tags     = var.default_tags
}

module "validationtool_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "validation-tool"
  tags     = var.default_tags
}

module "eyecue_iam_provider_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-iam-provider"
  tags     = var.default_tags
}

module "moonfire_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-moonfire-nvr"
  tags     = var.default_tags
}

module "moonfire_exporter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-moonfire-exporter"
  tags     = var.default_tags
}

module "tracker_features_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-tracker-features"
  tags     = var.default_tags
}

module "tracker_features_aggregations_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-tracker-features-aggregations"
  tags     = var.default_tags
}

module "monitor_recorder_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-monitor-recorder"
  tags     = var.default_tags
}

module "config_reloader_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-config-reloader"
  tags     = var.default_tags
}

module "event_handler_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-event-handler"
  tags     = var.default_tags
}

module "edge_metadata_gatekeeper_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "edge-metadata-gatekeeper"
  tags     = var.default_tags
}

module "camera_displacement_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "camera-displacement"
  tags     = var.default_tags
}

module "eyecue_weights_sync_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-weights-sync"
  tags     = var.default_tags
}

module "eyecue_camera_metrics_exporter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "eyecue-camera-metrics-exporter"
  tags     = var.default_tags
}

module "employee_counter_ecr" {
  source   = "../../../../modules/ecr"
  ecr_name = "employee-counter"
  tags     = var.default_tags
}
