#!/usr/bin/env bash

set -euo pipefail

REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT" # In case the script is run from a subdirectory

usage() {
  cat <<EOF
Usage: $0 [options]

This script applies the module.cw_log_retention* modules
to all environments that use CloudWatch log retention modules.

Options:
  --dry-run       Show what would be applied without actually applying
  --plan-only     Run terraform plan only (no apply)
  --auto-approve  Skip interactive approval for terraform apply
  --parallel      Run terraform operations in parallel (use with caution)
  -h, --help      Show this help message

Examples:
  $0 --dry-run                    # Show which environments would be affected
  $0 --plan-only                  # Run terraform plan on all environments
  $0 --auto-approve               # Apply without interactive confirmation
  $0 --parallel --auto-approve    # Apply in parallel (fastest but riskier)
EOF
  exit 0
}

# Parse command line arguments
DRY_RUN=0
PLAN_ONLY=0
AUTO_APPROVE=0
PARALLEL=0

while [ $# -gt 0 ]; do
  case "$1" in
    --dry-run) DRY_RUN=1 ;;
    --plan-only) PLAN_ONLY=1 ;;
    --auto-approve) AUTO_APPROVE=1 ;;
    --parallel) PARALLEL=1 ;;
    -h|--help) usage ;;
    *) error "Unknown option: $1" ;;
  esac
  shift
done

info() {
  printf "\033[0;34m%s\033[0m\n" "$1"
}

success() {
  printf "\033[0;32m%s\033[0m\n" "$1"
}

warning() {
  printf "\033[0;33m%s\033[0m\n" "$1"
}

error() {
  printf "\033[0;31m%s\033[0m\n" "$1"
  exit 1
}

# Find all environments that use the cw_log_retention module
find_environments_with_cw_log_retention() {
  find fingermark -name "*.tf" -type f -not -path "fingermark/_deprecated/*" | \
    xargs grep -l "source.*modules/cw_log_retention" | \
    xargs -r -n1 dirname | \
    sort -u
}

# Check if an environment uses cw_log_retention modules
environment_uses_cw_log_retention() {
  local env_dir="$1"
  # Check for cw_log_retention module usage
  grep -q "module.*cw_log_retention" "$env_dir"/*.tf 2>/dev/null
}

# Get all cw_log_retention module targets in an environment
get_cw_log_retention_targets() {
  local env_dir="$1"

  # Find all cw_log_retention module names in the environment
  grep -o "module \"[a-zA-Z0-9_]*cw_log_retention[a-zA-Z0-9_]*\"" "$env_dir"/*.tf 2>/dev/null | \
    sed 's/.*:module "/module./' | \
    sed 's/"$//' | \
    sort -u
}

# Get list of environments that have cw_log_retention modules configured
get_target_environments() {
  local environments
  environments=$(find_environments_with_cw_log_retention)

  for env in $environments; do
    if environment_uses_cw_log_retention "$env"; then
      echo "$env"
    fi
  done
}

# Run terraform operation on a single environment
run_terraform_on_environment() {
  local env_dir="$1"
  local operation="$2"  # "plan" or "apply"
  local target_paths
  local target_args=""

  target_paths=$(get_cw_log_retention_targets "$env_dir")

  if [ -z "$target_paths" ]; then
    warning "No cw_log_retention modules found in: $env_dir"
    return 0
  fi

  # Build target arguments for terraform
  for target in $target_paths; do
    target_args="$target_args -target=$target"
  done

  info "Running terraform $operation in: $env_dir (targeting: $(echo $target_paths | tr '\n' ' '))"

  (
    cd "$env_dir"

    # Initialize terraform
    terraform init -input=false

    # Run the specified operation targeting the cw_log_retention modules
    case "$operation" in
      "plan")
        terraform plan $target_args
        ;;
      "apply")
        if [ "$AUTO_APPROVE" -eq 1 ]; then
          terraform apply $target_args -auto-approve
        else
          terraform apply $target_args
        fi
        ;;
    esac
  )

  if [ $? -eq 0 ]; then
    success "✓ Successfully completed terraform $operation in: $env_dir"
  else
    error "✗ Failed terraform $operation in: $env_dir"
  fi
}

# Run terraform operations in parallel
run_parallel() {
  local operation="$1"
  local environments="$2"
  local process_pids=()

  for env in $environments; do
    run_terraform_on_environment "$env" "$operation" &
    process_pids+=($!)
  done

  # Wait for all background processes
  local failed=0
  for pid in "${process_pids[@]}"; do
    if ! wait "$pid"; then
      failed=1
    fi
  done

  return $failed
}

# Run terraform operations sequentially
run_sequential() {
  local operation="$1"
  local environments="$2"
  
  for env in $environments; do
    run_terraform_on_environment "$env" "$operation"
  done
}

# Main execution
main() {
  local target_environments
  target_environments=$(get_target_environments)
  
  if [ -z "$target_environments" ]; then
    warning "No environments found that use cw_log_retention modules."
    exit 0
  fi

  info "Found the following environments with cw_log_retention modules:"
  echo "$target_environments" | sed 's/^/  • /'
  echo

  if [ "$DRY_RUN" -eq 1 ]; then
    info "DRY RUN: Would target apply module.cw_log_retention* modules to the above environments"
    exit 0
  fi
  
  # Determine operation
  local operation="apply"
  if [ "$PLAN_ONLY" -eq 1 ]; then
    operation="plan"
  fi
  
  # Confirm before proceeding (unless auto-approve is set)
  if [ "$AUTO_APPROVE" -eq 0 ] && [ "$operation" = "apply" ]; then
    echo "This will run 'terraform apply -target=module.cw_log_retention*' on all listed environments."
    read -p "Do you want to continue? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      info "Operation cancelled."
      exit 0
    fi
  fi
  
  # Run terraform operations
  if [ "$PARALLEL" -eq 1 ]; then
    info "Running terraform $operation in parallel..."
    run_parallel "$operation" "$target_environments"
  else
    info "Running terraform $operation sequentially..."
    run_sequential "$operation" "$target_environments"
  fi
  
  success "✓ All terraform $operation operations completed successfully!"
}

main "$@"
